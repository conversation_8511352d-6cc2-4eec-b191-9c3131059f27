---
sidebar_position: 4
slug: /switch_to_milvus
---

# Switch to Milvus

Switch your doc engine from Elasticsearch to Milvus.

---

RAGFlow uses Elasticsearch by default for storing full text and vectors. To switch to [Milvus](https://milvus.io/), follow these steps:

:::caution WARNING
Switching to Milvus will require a separate Milvus service deployment and existing data migration.

:::

1. Deploy Milvus service:
   
   Follow the [Milvus installation guide](https://milvus.io/docs/install_standalone-docker.md) to deploy Milvus standalone or cluster mode.

2. Stop all running containers:

   ```bash
   $ docker compose -f docker/docker-compose.yml down -v
   ```

:::caution WARNING
`-v` will delete the docker container volumes, and the existing data will be cleared.
:::

3. Configure Milvus connection in **docker/service_conf.yaml**:

   ```yaml
   vector_store:
     name: milvus
     milvus:
       host: "localhost" # Replace with your Milvus host
       port: 19530 
       user: "" # Replace with your Milvus user if authentication is enabled
       password: "" # Replace with your Milvus password if authentication is enabled
       db_name: "ragflow" # Database name to use
       # Index parameters
       index_params:
         index_type: "HNSW" # Index type
         metric_type: "L2"   # Metric type
         params:
           M: 16
           efConstruction: 200
       # Search parameters
       search_params:
         ef: 50
   ```

4. Set `DOC_ENGINE` in **docker/.env** to `milvus`.

5. Start the containers:

   ```bash
   $ docker compose -f docker-compose.yml up -d
   ```

:::note
Milvus implementation in RAGFlow has the following limitations:
- Milvus doesn't support direct document updates, you need to delete and re-insert
- Full-text search is implemented using scalar field filtering
- Some advanced aggregation features may not be available
:::