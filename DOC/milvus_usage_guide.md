# Milvus适配使用指南

## 概述

本文档介绍了如何在RAGFlow中使用Milvus作为向量存储引擎。Milvus是一个专门用于向量相似度搜索的数据库，提供了高性能的向量检索能力。

## 功能特性

1. **向量存储**: 支持高维向量的存储和索引
2. **混合搜索**: 支持向量相似度搜索与标量字段过滤的组合
3. **高性能**: 利用Milvus的索引优化实现快速检索
4. **可扩展**: 支持Milvus集群部署以处理大规模数据

## 配置说明

### 1. 环境变量配置

在使用Milvus之前，需要设置以下环境变量：

```bash
export DOC_ENGINE=milvus
```

### 2. 配置文件设置

在`service_conf.yaml`中添加Milvus配置：

```yaml
vector_store:
  name: milvus
  milvus:
    host: "localhost"           # Milvus服务地址
    port: 19530                # Milvus服务端口
    user: ""                   # 用户名（如果启用了认证）
    password: ""               # 密码（如果启用了认证）
    db_name: "ragflow"         # 数据库名称
    # 索引参数
    index_params:
      index_type: "HNSW"       # 索引类型
      metric_type: "L2"        # 距离度量方式
      params:
        M: 16                  # HNSW参数
        efConstruction: 200    # HNSW参数
    # 搜索参数
    search_params:
      ef: 50                   # 搜索参数
```

## 使用限制

### 1. 更新操作限制
Milvus不支持直接更新文档，如需更新文档需要：
1. 删除原有文档
2. 插入新文档

### 2. 全文检索限制
Milvus专注于向量检索，全文检索功能通过标量字段过滤实现，功能相对有限。

### 3. 聚合查询限制
Milvus不支持复杂的聚合查询，相关功能在应用层实现。

## 性能优化建议

### 1. 索引选择
根据数据特点选择合适的索引类型：
- **HNSW**: 适合中小规模数据，查询精度高
- **IVF**: 适合大规模数据，内存占用相对较少

### 2. 参数调优
- `ef`: 搜索时的探索因子，值越大精度越高但速度越慢
- `M`: 图的度数，影响索引质量和查询性能
- `efConstruction`: 构建索引时的探索因子

### 3. 硬件配置
- 确保充足的内存以支持向量索引
- 使用SSD存储以提高I/O性能
- 对于大规模数据，考虑使用Milvus集群部署

## 故障排除

### 1. 连接问题
- 检查Milvus服务是否正常运行
- 验证网络连接和防火墙设置
- 确认用户名和密码正确

### 2. 性能问题
- 检查索引是否正确创建
- 调整搜索参数
- 监控系统资源使用情况

### 3. 数据一致性
- Milvus的写入是异步的，如需强一致性可调用flush操作
- 注意删除操作的延迟生效特性

## 迁移指南

从其他存储引擎迁移到Milvus：

1. **数据导出**: 从原存储引擎导出数据
2. **数据转换**: 转换数据格式以适配Milvus Schema
3. **数据导入**: 将数据导入到Milvus
4. **验证**: 验证数据完整性和查询功能

## 最佳实践

1. **定期维护**: 定期监控Milvus性能指标
2. **备份策略**: 制定数据备份和恢复计划
3. **监控告警**: 设置关键指标的监控和告警
4. **版本升级**: 关注Milvus版本更新，及时升级以获得新功能和性能改进