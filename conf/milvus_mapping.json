{"id": {"type": "<PERSON><PERSON><PERSON>", "max_length": 65535}, "doc_id": {"type": "<PERSON><PERSON><PERSON>", "max_length": 65535}, "kb_id": {"type": "<PERSON><PERSON><PERSON>", "max_length": 65535}, "create_time": {"type": "<PERSON><PERSON><PERSON>", "max_length": 65535}, "create_timestamp_flt": {"type": "float"}, "img_id": {"type": "<PERSON><PERSON><PERSON>", "max_length": 65535}, "docnm_kwd": {"type": "<PERSON><PERSON><PERSON>", "max_length": 65535}, "title_tks": {"type": "<PERSON><PERSON><PERSON>", "max_length": 65535}, "title_sm_tks": {"type": "<PERSON><PERSON><PERSON>", "max_length": 65535}, "name_kwd": {"type": "<PERSON><PERSON><PERSON>", "max_length": 65535}, "important_kwd": {"type": "<PERSON><PERSON><PERSON>", "max_length": 65535}, "tag_kwd": {"type": "<PERSON><PERSON><PERSON>", "max_length": 65535}, "important_tks": {"type": "<PERSON><PERSON><PERSON>", "max_length": 65535}, "question_kwd": {"type": "<PERSON><PERSON><PERSON>", "max_length": 65535}, "question_tks": {"type": "<PERSON><PERSON><PERSON>", "max_length": 65535}, "content_with_weight": {"type": "<PERSON><PERSON><PERSON>", "max_length": 65535}, "content_ltks": {"type": "<PERSON><PERSON><PERSON>", "max_length": 65535}, "content_sm_ltks": {"type": "<PERSON><PERSON><PERSON>", "max_length": 65535}, "authors_tks": {"type": "<PERSON><PERSON><PERSON>", "max_length": 65535}, "authors_sm_tks": {"type": "<PERSON><PERSON><PERSON>", "max_length": 65535}, "page_num_int": {"type": "<PERSON><PERSON><PERSON>", "max_length": 65535}, "top_int": {"type": "<PERSON><PERSON><PERSON>", "max_length": 65535}, "position_int": {"type": "<PERSON><PERSON><PERSON>", "max_length": 65535}, "weight_int": {"type": "int64"}, "weight_flt": {"type": "float"}, "rank_int": {"type": "int64"}, "rank_flt": {"type": "float"}, "available_int": {"type": "int64"}, "knowledge_graph_kwd": {"type": "<PERSON><PERSON><PERSON>", "max_length": 65535}, "entities_kwd": {"type": "<PERSON><PERSON><PERSON>", "max_length": 65535}, "pagerank_fea": {"type": "int64"}, "tag_feas": {"type": "<PERSON><PERSON><PERSON>", "max_length": 65535}, "from_entity_kwd": {"type": "<PERSON><PERSON><PERSON>", "max_length": 65535}, "to_entity_kwd": {"type": "<PERSON><PERSON><PERSON>", "max_length": 65535}, "entity_kwd": {"type": "<PERSON><PERSON><PERSON>", "max_length": 65535}, "entity_type_kwd": {"type": "<PERSON><PERSON><PERSON>", "max_length": 65535}, "source_id": {"type": "<PERSON><PERSON><PERSON>", "max_length": 65535}, "n_hop_with_weight": {"type": "<PERSON><PERSON><PERSON>", "max_length": 65535}, "removed_kwd": {"type": "<PERSON><PERSON><PERSON>", "max_length": 65535}, "doc_type_kwd": {"type": "<PERSON><PERSON><PERSON>", "max_length": 65535}}