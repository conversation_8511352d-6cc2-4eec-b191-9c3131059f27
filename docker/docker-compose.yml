include:
  - ./docker-compose-base.yml
# To ensure that the container processes the locally modified `service_conf.yaml.template` instead of the one included in its image, you need to mount the local `service_conf.yaml.template` to the container.
services:
  ragflow:
    depends_on:
      mysql:
        condition: service_healthy
    image: ${RAGFLOW_IMAGE}
    # Install pymilvus and start the application
    entrypoint: ["/start-with-milvus.sh"]
    # Example configuration to set up an MCP server:
    # command:
    #   - --enable-mcpserver
    #   - --mcp-host=0.0.0.0
    #   - --mcp-port=9382
    #   - --mcp-base-url=http://127.0.0.1:9380
    #   - --mcp-script-path=/ragflow/mcp/server/server.py
    #   - --mcp-mode=self-host
    #   - --mcp-host-api-key=ragflow-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
    # Optional transport flags for MCP (customize if needed).
    # Host mode need to combined with --no-transport-streamable-http-enabled flag, namely, host+streamable-http is not supported yet.
    # The following are enabled by default unless explicitly disabled with --no-<flag>.
    #   - --no-transport-sse-enabled # Disable legacy SSE endpoints (/sse and /messages/)
    #   - --no-transport-streamable-http-enabled #  Disable Streamable HTTP transport (/mcp endpoint)
    #   - --no-json-response # Disable JSON response mode in Streamable HTTP transport (instead of SSE over HTTP)
    container_name: ragflow-server
    ports:
      - ${SVR_HTTP_PORT}:9380
      - 8081:80
      - 8443:443
      - 5678:5678
      - 5679:5679
      - 9382:9382 # entry for MCP (host_port:docker_port). The docker_port must match the value you set for `mcp-port` above.
    volumes:
      - ./ragflow-logs:/ragflow/logs
      - ./nginx/ragflow.conf:/etc/nginx/conf.d/ragflow.conf
      - ./nginx/proxy.conf:/etc/nginx/proxy.conf
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ../history_data_agent:/ragflow/history_data_agent
      - ./service_conf.yaml.template:/ragflow/conf/service_conf.yaml.template
      - ./entrypoint.sh:/ragflow/entrypoint.sh
      # Mount modified files for Milvus support
      - ../rag/utils/milvus_conn.py:/ragflow/rag/utils/milvus_conn.py
      - ../api/settings.py:/ragflow/api/settings.py
      - ./start-with-milvus.sh:/start-with-milvus.sh
    env_file: .env
    environment:
      - TZ=${TIMEZONE}
      - HF_ENDPOINT=${HF_ENDPOINT-}
      - MACOS=${MACOS-}
    networks:
      - ragflow
    restart: on-failure
    # https://docs.docker.com/engine/daemon/prometheus/#create-a-prometheus-configuration
    # If you use Docker Desktop, the --add-host flag is optional. This flag ensures that the host's internal IP is exposed to the Prometheus container.
    extra_hosts:
      - "host.docker.internal:host-gateway"
  # executor:
  #   depends_on:
  #     mysql:
  #       condition: service_healthy
  #   image: ${RAGFLOW_IMAGE}
  #   container_name: ragflow-executor
  #   volumes:
  #     - ./ragflow-logs:/ragflow/logs
  #     - ./nginx/ragflow.conf:/etc/nginx/conf.d/ragflow.conf
  #   env_file: .env
  #   environment:
  #     - TZ=${TIMEZONE}
  #     - HF_ENDPOINT=${HF_ENDPOINT}
  #     - MACOS=${MACOS}
  #   entrypoint: "/ragflow/entrypoint_task_executor.sh 1 3"
  #   networks:
  #     - ragflow
  #   restart: on-failure
  #   # https://docs.docker.com/engine/daemon/prometheus/#create-a-prometheus-configuration
  #   # If you're using Docker Desktop, the --add-host flag is optional. This flag makes sure that the host's internal IP gets exposed to the Prometheus container.
  #   extra_hosts:
  #     - "host.docker.internal:host-gateway"
