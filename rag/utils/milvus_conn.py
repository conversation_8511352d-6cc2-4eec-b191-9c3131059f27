#
#  Copyright 2025 The InfiniFlow Authors. All Rights Reserved.
#
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
#

import logging
import re
import json
import time
import copy
import numpy as np
import pandas as pd

def install_pymilvus():
    """动态安装pymilvus包"""
    import subprocess
    import sys
    import os

    # 尝试多种安装方法
    install_commands = [
        [sys.executable, "-m", "pip", "install", "pymilvus"],
        ["pip", "install", "pymilvus"],
        ["pip3", "install", "pymilvus"],
        ["python", "-m", "pip", "install", "pymilvus"],
        ["python3", "-m", "pip", "install", "pymilvus"]
    ]

    for cmd in install_commands:
        try:
            subprocess.check_call(cmd, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            continue

    return False

try:
    from pymilvus import (
        MilvusClient,
        MilvusException,
        DataType,
        CollectionSchema,
        FieldSchema,
        Collection,
        connections,
        utility,
        AnnSearchRequest,
        RRFRanker,
        Hits,
        SearchResult
    )
except ImportError:
    # 尝试安装pymilvus
    if install_pymilvus():
        try:
            from pymilvus import (
                MilvusClient,
                MilvusException,
                DataType,
                CollectionSchema,
                FieldSchema,
                Collection,
                connections,
                utility,
                AnnSearchRequest,
                RRFRanker,
                Hits,
                SearchResult
            )
        except ImportError:
            # 如果安装后仍然无法导入，设置为None
            MilvusClient = None
            MilvusException = Exception
            DataType = None
            CollectionSchema = None
            FieldSchema = None
            Collection = None
            connections = None
            utility = None
            AnnSearchRequest = None
            RRFRanker = None
            Hits = None
            SearchResult = None
    else:
        # 如果安装失败，设置为None
        MilvusClient = None
        MilvusException = Exception
        DataType = None
        CollectionSchema = None
        FieldSchema = None
        Collection = None
        connections = None
        utility = None
        AnnSearchRequest = None
        RRFRanker = None
        Hits = None
        SearchResult = None

from rag.settings import PAGERANK_FLD, TAG_FLD
from rag.utils import singleton
from api.utils.file_utils import get_project_base_directory
from rag.utils.doc_store_conn import (
    DocStoreConnection,
    MatchExpr,
    MatchTextExpr,
    MatchDenseExpr,
    FusionExpr,
    OrderByExpr,
)

logger = logging.getLogger('ragflow.milvus_conn')

def field_keyword(field_name: str):
    # The "docnm_kwd" field is always a string, not list.
    if field_name == "source_id" or (field_name.endswith("_kwd") and field_name != "docnm_kwd" and field_name != "knowledge_graph_kwd"):
        return True
    return False

def equivalent_condition_to_milvus_expr(condition: dict) -> str:
    """
    Convert condition dict to Milvus expression string
    """
    if not condition:
        return ""
    
    expr_parts = []
    for k, v in condition.items():
        if k == "_id" or not v:
            continue
            
        if isinstance(v, list):
            # Handle list values with 'in' expression
            if field_keyword(k):
                # For keyword fields, we need to handle each item
                in_conditions = []
                for item in v:
                    if isinstance(item, str):
                        item = item.replace("'", "\\'")
                        in_conditions.append(f"'{item}'")
                    else:
                        in_conditions.append(str(item))
                if in_conditions:
                    expr_parts.append(f"{k} in [{', '.join(in_conditions)}]")
            else:
                # For non-keyword fields
                in_conditions = []
                for item in v:
                    if isinstance(item, str):
                        item = item.replace("'", "\\'")
                        in_conditions.append(f"'{item}'")
                    else:
                        in_conditions.append(str(item))
                if in_conditions:
                    expr_parts.append(f"{k} in [{', '.join(in_conditions)}]")
        elif isinstance(v, str):
            # Handle string values
            v = v.replace("'", "\\'")
            expr_parts.append(f"{k} == '{v}'")
        else:
            # Handle numeric values
            expr_parts.append(f"{k} == {v}")
    
    return " and ".join(expr_parts) if expr_parts else ""

def concat_dataframes(df_list: list[pd.DataFrame], selectFields: list[str]) -> pd.DataFrame:
    df_list2 = [df for df in df_list if not df.empty]
    if df_list2:
        return pd.concat(df_list2, axis=0).reset_index(drop=True)
    
    schema = []
    for field_name in selectFields:
        if field_name == 'score()':  # Workaround: fix schema is changed to score()
            schema.append('SCORE')
        elif field_name == 'similarity()':  # Workaround: fix schema is changed to similarity()
            schema.append('SIMILARITY')
        else:
            schema.append(field_name)
    return pd.DataFrame(columns=schema)

@singleton
class MilvusConnection(DocStoreConnection):
    def __init__(self):
        # 避免循环导入，直接从配置文件获取设置
        from api.utils import get_base_config
        self.milvus_config = get_base_config("milvus", {
            "host": "localhost",
            "port": 19530,
            "user": "",
            "password": "",
            "db_name": "ragflow"
        })
        self.dbName = self.milvus_config.get("db_name", "ragflow")
        
        # Initialize Milvus connection
        if MilvusClient is None:
            logger.warning("pymilvus is not available. Milvus functionality will be disabled.")
            return
            
        try:
            # Connect to Milvus
            connections.connect(
                alias="default",
                host=self.milvus_config.get("host", "localhost"),
                port=self.milvus_config.get("port", 19530),
                user=self.milvus_config.get("user", ""),
                password=self.milvus_config.get("password", ""),
            )
            logger.info(f"Connected to Milvus at {self.milvus_config.get('host')}:{self.milvus_config.get('port')}")
        except Exception as e:
            logger.error(f"Failed to connect to Milvus: {str(e)}")
            raise Exception(f"Failed to connect to Milvus: {str(e)}")
            
        # Initialize Milvus client
        self.client = MilvusClient(
            uri=f"http://{self.milvus_config.get('host', 'localhost')}:{self.milvus_config.get('port', 19530)}",
            token=f"{self.milvus_config.get('user', '')}:{self.milvus_config.get('password', '')}" if self.milvus_config.get("user") else None
        )
        
        # Ensure database exists
        try:
            if self.dbName not in self.client.list_databases():
                self.client.create_database(db_name=self.dbName)
            self.client.using_database(db_name=self.dbName)
            logger.info(f"Using Milvus database: {self.dbName}")
        except Exception as e:
            logger.warning(f"Database operations failed: {str(e)}")

    """
    Database operations
    """

    def dbType(self) -> str:
        return "milvus"

    def health(self) -> dict:
        """
        Return the health status of the database.
        """
        try:
            # Check if connection is alive
            utility.has_collection("test_collection")
            return {
                "type": "milvus",
                "status": "green",
                "error": ""
            }
        except Exception as e:
            return {
                "type": "milvus",
                "status": "red",
                "error": str(e)
            }

    """
    Table operations
    """

    def createIdx(self, indexName: str, knowledgebaseId: str, vectorSize: int):
        collection_name = f"{indexName}_{knowledgebaseId}"
        
        # Check if collection already exists
        if self.indexExist(indexName, knowledgebaseId):
            logger.info(f"Collection {collection_name} already exists")
            return
            
        try:
            # Define schema
            fields = [
                FieldSchema(name="id", dtype=DataType.VARCHAR, max_length=65535, is_primary=True),
                FieldSchema(name="doc_id", dtype=DataType.VARCHAR, max_length=65535),
                FieldSchema(name="kb_id", dtype=DataType.VARCHAR, max_length=65535),
                FieldSchema(name="content_with_weight", dtype=DataType.VARCHAR, max_length=65535),
                FieldSchema(name="content_ltks", dtype=DataType.VARCHAR, max_length=65535),
                FieldSchema(name="docnm_kwd", dtype=DataType.VARCHAR, max_length=65535),
                FieldSchema(name="title_tks", dtype=DataType.VARCHAR, max_length=65535),
                FieldSchema(name="create_time", dtype=DataType.VARCHAR, max_length=65535),
                FieldSchema(name="create_timestamp_flt", dtype=DataType.FLOAT),
                FieldSchema(name="img_id", dtype=DataType.VARCHAR, max_length=65535),
                FieldSchema(name="available_int", dtype=DataType.INT64),
                FieldSchema(name="page_num_int", dtype=DataType.VARCHAR, max_length=65535),
                FieldSchema(name="top_int", dtype=DataType.VARCHAR, max_length=65535),
                FieldSchema(name="position_int", dtype=DataType.VARCHAR, max_length=65535),
                FieldSchema(name="weight_int", dtype=DataType.INT64),
                FieldSchema(name="weight_flt", dtype=DataType.FLOAT),
                FieldSchema(name="rank_int", dtype=DataType.INT64),
                FieldSchema(name="rank_flt", dtype=DataType.FLOAT),
                FieldSchema(name="pagerank_fea", dtype=DataType.INT64),
                FieldSchema(name="tag_feas", dtype=DataType.VARCHAR, max_length=65535),
                FieldSchema(name="important_kwd", dtype=DataType.VARCHAR, max_length=65535),
                FieldSchema(name="question_kwd", dtype=DataType.VARCHAR, max_length=65535),
                FieldSchema(name="question_tks", dtype=DataType.VARCHAR, max_length=65535),
                FieldSchema(name="knowledge_graph_kwd", dtype=DataType.VARCHAR, max_length=65535),
                FieldSchema(name="entities_kwd", dtype=DataType.VARCHAR, max_length=65535),
                FieldSchema(name="from_entity_kwd", dtype=DataType.VARCHAR, max_length=65535),
                FieldSchema(name="to_entity_kwd", dtype=DataType.VARCHAR, max_length=65535),
                FieldSchema(name="entity_kwd", dtype=DataType.VARCHAR, max_length=65535),
                FieldSchema(name="entity_type_kwd", dtype=DataType.VARCHAR, max_length=65535),
                FieldSchema(name="source_id", dtype=DataType.VARCHAR, max_length=65535),
                FieldSchema(name="n_hop_with_weight", dtype=DataType.VARCHAR, max_length=65535),
                FieldSchema(name="removed_kwd", dtype=DataType.VARCHAR, max_length=65535),
                FieldSchema(name="doc_type_kwd", dtype=DataType.VARCHAR, max_length=65535),
                # Vector field
                FieldSchema(name=f"q_{vectorSize}_vec", dtype=DataType.FLOAT_VECTOR, dim=vectorSize)
            ]
            
            schema = CollectionSchema(fields, description=f"Collection for {collection_name}")
            
            # Create collection
            collection = Collection(
                name=collection_name,
                schema=schema,
                using='default'
            )
            
            # Create index for vector field
            index_params = {
                "index_type": self.milvus_config.get("index_params", {}).get("index_type", "HNSW"),
                "metric_type": self.milvus_config.get("index_params", {}).get("metric_type", "L2"),
                "params": self.milvus_config.get("index_params", {}).get("params", {"M": 16, "efConstruction": 200})
            }
            
            collection.create_index(
                field_name=f"q_{vectorSize}_vec",
                index_params=index_params
            )
            
            # Load collection
            collection.load()
            
            logger.info(f"Created Milvus collection {collection_name} with vector size {vectorSize}")
            
        except Exception as e:
            logger.error(f"Failed to create collection {collection_name}: {str(e)}")
            raise Exception(f"Failed to create collection {collection_name}: {str(e)}")

    def deleteIdx(self, indexName: str, knowledgebaseId: str):
        collection_name = f"{indexName}_{knowledgebaseId}"
        try:
            if utility.has_collection(collection_name):
                utility.drop_collection(collection_name)
                logger.info(f"Dropped Milvus collection {collection_name}")
        except Exception as e:
            logger.warning(f"Failed to drop collection {collection_name}: {str(e)}")

    def indexExist(self, indexName: str, knowledgebaseId: str) -> bool:
        collection_name = f"{indexName}_{knowledgebaseId}"
        try:
            return utility.has_collection(collection_name)
        except Exception as e:
            logger.warning(f"Failed to check collection existence {collection_name}: {str(e)}")
            return False

    """
    CRUD operations
    """

    def search(
            self, selectFields: list[str],
            highlightFields: list[str],
            condition: dict,
            matchExprs: list[MatchExpr],
            orderBy: OrderByExpr,
            offset: int,
            limit: int,
            indexNames: str | list[str],
            knowledgebaseIds: list[str],
            aggFields: list[str] = [],
            rank_feature: dict | None = None
    ) -> tuple[pd.DataFrame, int]:
        """
        Search with given conjunctive equivalent filtering condition and return all fields of matched documents
        """
        if isinstance(indexNames, str):
            indexNames = indexNames.split(",")
        assert isinstance(indexNames, list) and len(indexNames) > 0

        df_list = list()
        total_hits_count = 0
        
        # Prepare filter expression
        filter_expr = equivalent_condition_to_milvus_expr(condition)
        if "kb_id" in condition:
            # Handle knowledgebaseIds filter
            kb_filter = " or ".join([f"kb_id == '{kb_id}'" for kb_id in knowledgebaseIds])
            if filter_expr:
                filter_expr = f"({filter_expr}) and ({kb_filter})"
            else:
                filter_expr = kb_filter

        # Process match expressions
        vector_search_requests = []
        text_filter_expr = filter_expr
        
        for matchExpr in matchExprs:
            if isinstance(matchExpr, MatchDenseExpr):
                # Vector search
                vector_field = matchExpr.vector_column_name
                query_vector = matchExpr.embedding_data
                topk = matchExpr.topn
                metric_type = matchExpr.distance_type.upper()
                
                # Create search parameters
                search_params = {
                    "metric_type": metric_type,
                    "params": self.milvus_config.get("search_params", {"ef": 50})
                }
                
                search_request = AnnSearchRequest(
                    data=[query_vector],
                    anns_field=vector_field,
                    param=search_params,
                    limit=topk
                )
                vector_search_requests.append(search_request)
                
            elif isinstance(matchExpr, MatchTextExpr):
                # Text search - convert to filter expressions
                # For Milvus, we'll use scalar field filtering
                for field in matchExpr.fields:
                    # Simple keyword matching
                    if matchExpr.matching_text:
                        text_expr = f"{field} like '%{matchExpr.matching_text}%'"
                        if text_filter_expr:
                            text_filter_expr = f"({text_filter_expr}) and ({text_expr})"
                        else:
                            text_filter_expr = text_expr

        # Search across all collections
        for indexName in indexNames:
            for knowledgebaseId in knowledgebaseIds:
                collection_name = f"{indexName}_{knowledgebaseId}"
                try:
                    if not utility.has_collection(collection_name):
                        continue
                        
                    collection = Collection(collection_name)
                    collection.load()
                    
                    # Perform search
                    if vector_search_requests:
                        # Hybrid search with vector and scalar filtering
                        res = collection.hybrid_search(
                            reqs=vector_search_requests,
                            ranker=RRFRanker(),
                            limit=limit,
                            offset=offset,
                            output_fields=selectFields,
                            expr=text_filter_expr if text_filter_expr else None
                        )
                    else:
                        # Scalar filtering only
                        res = collection.query(
                            expr=text_filter_expr if text_filter_expr else "",
                            output_fields=selectFields,
                            offset=offset,
                            limit=limit
                        )
                    
                    # Convert results to DataFrame
                    if res:
                        df = pd.DataFrame(res)
                        df_list.append(df)
                        total_hits_count += len(res)
                        
                except Exception as e:
                    logger.warning(f"Search failed for collection {collection_name}: {str(e)}")
                    continue

        # Concatenate results
        final_df = concat_dataframes(df_list, selectFields)
        logger.debug(f"Milvus search result: {str(final_df)}")
        return final_df, total_hits_count

    def get(self, chunkId: str, indexName: str, knowledgebaseIds: list[str]) -> dict | None:
        df_list = list()
        
        for knowledgebaseId in knowledgebaseIds:
            collection_name = f"{indexName}_{knowledgebaseId}"
            try:
                if not utility.has_collection(collection_name):
                    continue
                    
                collection = Collection(collection_name)
                collection.load()
                
                # Query by ID
                res = collection.query(
                    expr=f"id == '{chunkId}'",
                    output_fields=["*"]
                )
                
                if res:
                    df = pd.DataFrame(res)
                    df_list.append(df)
                    
            except Exception as e:
                logger.warning(f"Get failed for collection {collection_name}: {str(e)}")
                continue

        # Concatenate results
        final_df = concat_dataframes(df_list, ["id"])
        if not final_df.empty:
            # Return first result as dict
            result_dict = final_df.iloc[0].to_dict()
            return result_dict
            
        return None

    def insert(self, documents: list[dict], indexName: str, knowledgebaseId: str = None) -> list[str]:
        collection_name = f"{indexName}_{knowledgebaseId}"
        
        try:
            if not utility.has_collection(collection_name):
                # Need to create collection first - infer vector size from documents
                vector_size = 0
                patt = re.compile(r"q_(?P<vector_size>\d+)_vec")
                for k in documents[0].keys():
                    m = patt.match(k)
                    if m:
                        vector_size = int(m.group("vector_size"))
                        break
                if vector_size == 0:
                    raise ValueError("Cannot infer vector size from documents")
                self.createIdx(indexName, knowledgebaseId, vector_size)
            
            collection = Collection(collection_name)
            collection.load()
            
            # Process documents
            processed_docs = []
            for doc in documents:
                processed_doc = copy.deepcopy(doc)
                
                # Handle special field types
                for k, v in processed_doc.items():
                    if field_keyword(k):
                        if isinstance(v, list):
                            processed_doc[k] = "###".join(str(item) for item in v)
                        else:
                            processed_doc[k] = str(v) if v is not None else ""
                    elif re.search(r"_feas$", k):
                        processed_doc[k] = json.dumps(v) if v is not None else "{}"
                    elif k == 'kb_id':
                        if isinstance(processed_doc[k], list):
                            processed_doc[k] = processed_doc[k][0] if processed_doc[k] else ""
                    elif k == "position_int":
                        if isinstance(v, list):
                            arr = [num for row in v for num in row]
                            processed_doc[k] = "_".join(f"{num:08x}" for num in arr)
                    elif k in ["page_num_int", "top_int"]:
                        if isinstance(v, list):
                            processed_doc[k] = "_".join(f"{num:08x}" for num in v)
                
                processed_docs.append(processed_doc)
            
            # Insert documents
            collection.insert(processed_docs)
            collection.flush()
            
            logger.debug(f"Inserted {len(processed_docs)} documents into {collection_name}")
            return []
            
        except Exception as e:
            logger.error(f"Insert failed for collection {collection_name}: {str(e)}")
            raise Exception(f"Insert failed: {str(e)}")

    def update(self, condition: dict, newValue: dict, indexName: str, knowledgebaseId: str) -> bool:
        collection_name = f"{indexName}_{knowledgebaseId}"
        
        try:
            if not utility.has_collection(collection_name):
                logger.warning(f"Collection {collection_name} does not exist")
                return False
                
            collection = Collection(collection_name)
            collection.load()
            
            # Convert condition to expression
            expr = equivalent_condition_to_milvus_expr(condition)
            
            # Process new values
            processed_new_value = {}
            for k, v in newValue.items():
                if field_keyword(k):
                    if isinstance(v, list):
                        processed_new_value[k] = "###".join(str(item) for item in v)
                    else:
                        processed_new_value[k] = str(v) if v is not None else ""
                elif re.search(r"_feas$", k):
                    processed_new_value[k] = json.dumps(v) if v is not None else "{}"
                elif k == 'kb_id':
                    if isinstance(v, list):
                        processed_new_value[k] = v[0] if v else ""
                elif k == "position_int":
                    if isinstance(v, list):
                        arr = [num for row in v for num in row]
                        processed_new_value[k] = "_".join(f"{num:08x}" for num in arr)
                elif k in ["page_num_int", "top_int"]:
                    if isinstance(v, list):
                        processed_new_value[k] = "_".join(f"{num:08x}" for num in v)
                else:
                    processed_new_value[k] = v
            
            # Milvus doesn't support direct update, need to delete and re-insert
            # This is a limitation - for now we'll just log a warning
            logger.warning(f"Milvus doesn't support direct update. Please delete and re-insert documents matching: {expr}")
            return False
            
        except Exception as e:
            logger.error(f"Update failed for collection {collection_name}: {str(e)}")
            return False

    def delete(self, condition: dict, indexName: str, knowledgebaseId: str) -> int:
        collection_name = f"{indexName}_{knowledgebaseId}"
        
        try:
            if not utility.has_collection(collection_name):
                logger.warning(f"Collection {collection_name} does not exist")
                return 0
                
            collection = Collection(collection_name)
            collection.load()
            
            # Convert condition to expression
            expr = equivalent_condition_to_milvus_expr(condition)
            if not expr:
                logger.warning("Empty delete condition")
                return 0
            
            # Perform delete
            res = collection.delete(expr)
            deleted_count = len(res) if res else 0
            
            logger.debug(f"Deleted {deleted_count} documents from {collection_name} with condition: {expr}")
            return deleted_count
            
        except Exception as e:
            logger.error(f"Delete failed for collection {collection_name}: {str(e)}")
            return 0

    """
    Helper functions for search result
    """

    def getTotal(self, res: tuple[pd.DataFrame, int] | pd.DataFrame) -> int:
        if isinstance(res, tuple):
            return res[1]
        return len(res)

    def getChunkIds(self, res: tuple[pd.DataFrame, int] | pd.DataFrame) -> list[str]:
        if isinstance(res, tuple):
            res = res[0]
        if 'id' in res.columns:
            return list(res["id"])
        return []

    def getFields(self, res: tuple[pd.DataFrame, int] | pd.DataFrame, fields: list[str]) -> dict[str, dict]:
        if isinstance(res, tuple):
            res = res[0]
        if not fields or res.empty:
            return {}
            
        result_dict = {}
        for _, row in res.iterrows():
            if 'id' in row:
                chunk_id = row['id']
                field_data = {}
                for field in fields:
                    if field in row and row[field] is not None:
                        value = row[field]
                        # Handle special field types
                        if field_keyword(field):
                            if isinstance(value, str):
                                field_data[field] = [kwd for kwd in value.split("###") if kwd]
                            else:
                                field_data[field] = value
                        elif re.search(r"_feas$", field):
                            if isinstance(value, str):
                                try:
                                    field_data[field] = json.loads(value)
                                except:
                                    field_data[field] = {}
                            else:
                                field_data[field] = value
                        elif field == "position_int":
                            if isinstance(value, str):
                                try:
                                    arr = [int(hex_val, 16) for hex_val in value.split('_')]
                                    field_data[field] = [arr[i:i + 5] for i in range(0, len(arr), 5)]
                                except:
                                    field_data[field] = []
                            else:
                                field_data[field] = value
                        elif field in ["page_num_int", "top_int"]:
                            if isinstance(value, str):
                                try:
                                    field_data[field] = [int(hex_val, 16) for hex_val in value.split('_')]
                                except:
                                    field_data[field] = []
                            else:
                                field_data[field] = value
                        else:
                            field_data[field] = value
                    else:
                        field_data[field] = None
                result_dict[chunk_id] = field_data
                
        return result_dict

    def getHighlight(self, res: tuple[pd.DataFrame, int] | pd.DataFrame, keywords: list[str], fieldnm: str):
        if isinstance(res, tuple):
            res = res[0]
        ans = {}
        
        if fieldnm not in res.columns:
            return {}
            
        for _, row in res.iterrows():
            if 'id' in row and fieldnm in row:
                chunk_id = row['id']
                txt = row[fieldnm]
                if txt and isinstance(txt, str):
                    txt = re.sub(r"[\r\n]", " ", txt, flags=re.IGNORECASE | re.MULTILINE)
                    txts = []
                    for t in re.split(r"[.?!;\n]", txt):
                        for w in keywords:
                            t = re.sub(
                                r"(^|[ .?/'\"\(\)!,:;-])(%s)([ .?/'\"\(\)!,:;-])" % re.escape(w),
                                r"\1<em>\2</em>\3",
                                t,
                                flags=re.IGNORECASE | re.MULTILINE,
                            )
                        if re.search(r"<em>[^<>]+</em>", t, flags=re.IGNORECASE | re.MULTILINE):
                            txts.append(t)
                    ans[chunk_id] = "...".join(txts)
                    
        return ans

    def getAggregation(self, res: tuple[pd.DataFrame, int] | pd.DataFrame, fieldnm: str):
        """
        Manual aggregation for tag fields since Milvus doesn't provide native aggregation
        """
        from collections import Counter

        # Extract DataFrame from result
        if isinstance(res, tuple):
            df, _ = res
        else:
            df = res

        if df.empty or fieldnm not in df.columns:
            return []

        # Aggregate tag counts
        tag_counter = Counter()

        for _, row in df.iterrows():
            value = row[fieldnm]
            if pd.isna(value) or not value:
                continue

            # Handle different tag formats
            if isinstance(value, str):
                # Split by ### for tag_kwd field or comma for other formats
                if fieldnm == "tag_kwd" and "###" in value:
                    tags = [tag.strip() for tag in value.split("###") if tag.strip()]
                else:
                    # Try comma separation as fallback
                    tags = [tag.strip() for tag in value.split(",") if tag.strip()]

                for tag in tags:
                    if tag:  # Only count non-empty tags
                        tag_counter[tag] += 1
            elif isinstance(value, list):
                # Handle list format
                for tag in value:
                    if tag and isinstance(tag, str):
                        tag_counter[tag.strip()] += 1

        # Return as list of [tag, count] pairs, sorted by count descending
        return [[tag, count] for tag, count in tag_counter.most_common()]

    """
    SQL
    """
    
    def sql(self, sql: str, fetch_size: int, format: str):
        raise NotImplementedError("SQL interface not implemented for Milvus")