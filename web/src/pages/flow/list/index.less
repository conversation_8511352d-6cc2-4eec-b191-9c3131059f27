.flowListWrapper {
  padding: 48px;
}

.topWrapper {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 0 60px 72px;

  .title {
    font-family: Inter;
    font-size: 30px;
    font-style: normal;
    font-weight: @fontWeight600;
    line-height: 38px;
    color: rgba(16, 24, 40, 1);
  }
  .description {
    font-family: Inter;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    color: rgba(71, 84, 103, 1);
  }

  .topButton {
    font-family: Inter;
    font-size: 14px;
    font-style: normal;
    font-weight: @fontWeight600;
    line-height: 20px;
  }

  .filterButton {
    display: flex;
    align-items: center;
    .topButton();
  }
}
.flowCardContainer {
  padding: 0 60px;
  overflow: auto;
  .knowledgeEmpty {
    width: 100%;
  }
}

.templatesBox {
  max-height: 70vh;
  overflow: auto;
}

.agentTemplateModal {
  top: 0;
  margin: 0;
  width: 100%;
  max-width: 100%;
  height: 100vh;
  max-height: 100vh;
  padding: 0;

  :global(.ant-modal-content) {
    // width: 100vw;
    height: 100%;
    border-radius: 0;
  }
  .agentDescription {
    padding-top: 18px;
    height: 110px;
  }
  .createModalContent {
    height: 90vh;
  }
  .agentTitleWrapper {
    width: 80%;
  }
  .flowTemplateCard {
    position: relative;
    cursor: pointer;
  }

  .selectedFlowTemplateCard {
    background-color: @selectedBackgroundColor;
  }
  .useButton {
    position: absolute;
    width: 84%;
    left: 0;
    right: 0;
    bottom: 10px;
    margin: auto;
  }
}

.agentTemplateModalWrapper {
  margin: 0;
}
