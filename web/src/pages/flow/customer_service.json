{"edges": [{"id": "63a2f242-8e71-4098-a46a-459a76d538bd", "label": "", "source": "begin", "target": "answer:0"}, {"id": "cc6dd8bb-e9dc-46e8-9009-5b96f98ae6c0", "label": "", "source": "generate:casual", "target": "answer:0"}, {"id": "58dbf05a-07fc-4a0a-8c03-5f9117e48c35", "label": "", "source": "generate:answer", "target": "answer:0"}, {"id": "dd0ff4f2-4d75-4e7d-a505-3e9533402823", "label": "", "source": "generate:complain", "target": "answer:0"}, {"id": "3dc7a511-9cde-4080-a572-6b06a64e0458", "label": "", "source": "generate:ask_contact", "target": "answer:0"}, {"id": "20e31fee-c392-4257-860a-5844d264198e", "label": "", "source": "message:get_contact", "target": "answer:0"}, {"id": "104ac8bb-5d75-4eca-8065-1d8fc2805b8e", "label": "", "source": "answer:0", "target": "categorize:0"}, {"id": "755864b5-9eef-44ec-a560-9a23b5a3f9ee", "label": "", "source": "categorize:0", "target": "retrieval:0", "sourceHandle": "product_related"}, {"id": "7f68384d-3441-4bfa-bf13-69af67e857d2", "label": "", "source": "categorize:0", "target": "generate:casual", "sourceHandle": "casual"}, {"id": "c9bf8e81-9345-4885-b565-be2f5b16f6ef", "label": "", "source": "categorize:0", "target": "generate:complain", "sourceHandle": "complain"}, {"id": "2f326699-621b-4d28-ab98-70d99ad21add", "label": "", "source": "categorize:0", "target": "message:get_contact", "sourceHandle": "answer"}, {"id": "03e45174-55df-47d6-8e5f-fbe2ffc148d7", "label": "", "source": "retrieval:0", "target": "relevant:0"}, {"id": "a26027ac-e8a9-48e4-814c-3262b8d81913", "label": "", "source": "relevant:0", "target": "generate:answer"}, {"id": "04d99dc7-6120-4169-98c6-7bc2813aa85b", "label": "", "source": "relevant:0", "target": "generate:ask_contact"}], "nodes": [{"id": "begin", "type": "beginNode", "position": {"x": 0, "y": 0}, "data": {"label": "<PERSON><PERSON>", "name": "LegalPoetsAttack", "form": {"prologue": "Hi! How can I help you?"}}, "sourcePosition": "left", "targetPosition": "right"}, {"id": "answer:0", "type": "ragNode", "position": {"x": 0, "y": 0}, "data": {"label": "Answer", "name": "ThreeGeeseBehave", "form": {}}, "sourcePosition": "left", "targetPosition": "right"}, {"id": "categorize:0", "type": "categorizeNode", "position": {"x": 0, "y": 0}, "data": {"label": "Categorize", "name": "PublicComicsHammer", "form": {"llm_id": "deepseek-chat", "category_description": {"product_related": {"description": "The question is about the product usage, appearance and how it works.", "examples": "Why it always beaming?\nHow to install it onto the wall?\nIt leaks, what to do?\nException: Can't connect to ES cluster\nHow to build the RAGFlow image from scratch", "to": "retrieval:0"}, "casual": {"description": "The question is not about the product usage, appearance and how it works. Just casual chat.", "examples": "How are you doing?\nWhat is your name?\nAre you a robot?\nWhat's the weather?\nWill it rain?", "to": "generate:casual"}, "complain": {"description": "<PERSON><PERSON><PERSON> even curse about the product or service you provide. But the comment is not specific enough.", "examples": "How bad is it.\nIt's really sucks.\nDamn, for God's sake, can it be more steady?\nShit, I just can't use this shit.\nI can't stand it anymore.", "to": "generate:complain"}, "answer": {"description": "This answer provide a specific contact information, like e-mail, phone number, wechat number, line number, twitter, discord, etc,.", "examples": "My phone number is 203921\nkevin<PERSON>.<EMAIL>\nThis is my discord number: johndo<PERSON>_29384", "to": "message:get_contact"}}, "message_history_window_size": 8}}, "sourcePosition": "left", "targetPosition": "right"}, {"id": "generate:casual", "type": "ragNode", "position": {"x": 0, "y": 0}, "data": {"label": "Generate", "name": "SourKnivesPay", "form": {"llm_id": "deepseek-chat", "prompt": "You are a customer support. But the customer wants to have a casual chat with you instead of consulting about the product. Be nice, funny, enthusiasm and concern.", "temperature": 0.9, "message_history_window_size": 12, "cite": false}}, "sourcePosition": "left", "targetPosition": "right"}, {"id": "generate:complain", "type": "ragNode", "position": {"x": 0, "y": 0}, "data": {"label": "Generate", "name": "TameLlamasSniff", "form": {"llm_id": "deepseek-chat", "prompt": "You are a customer support. the Customers complain even curse about the products but not specific enough. You need to ask him/her what's the specific problem with the product. Be nice, patient and concern to soothe your customers’ emotions at first place.", "temperature": 0.9, "message_history_window_size": 12, "cite": false}}, "sourcePosition": "left", "targetPosition": "right"}, {"id": "retrieval:0", "type": "ragNode", "position": {"x": 0, "y": 0}, "data": {"label": "Retrieval", "name": "ShinyPathsDraw", "form": {"similarity_threshold": 0.2, "keywords_similarity_weight": 0.3, "top_n": 6, "top_k": 1024, "rerank_id": "BAAI/bge-reranker-v2-m3", "kb_ids": ["869a236818b811ef91dffa163e197198"]}}, "sourcePosition": "left", "targetPosition": "right"}, {"id": "relevant:0", "type": "relevantNode", "position": {"x": 0, "y": 0}, "data": {"label": "Relevant", "name": "LegalPotsLick", "form": {"llm_id": "deepseek-chat", "temperature": 0.02, "yes": "generate:answer", "no": "generate:ask_contact"}}, "sourcePosition": "left", "targetPosition": "right"}, {"id": "generate:answer", "type": "ragNode", "position": {"x": 0, "y": 0}, "data": {"label": "Generate", "name": "YellowGamesReport", "form": {"llm_id": "deepseek-chat", "prompt": "You are an intelligent assistant. Please answer the question based on content of knowledge base. When all knowledge base content is irrelevant to the question, your answer must include the sentence \"The answer you are looking for is not found in the knowledge base!\". Answers need to consider chat history.\n      Knowledge base content is as following:\n      {input}\n      The above is the content of knowledge base.", "temperature": 0.02}}, "sourcePosition": "left", "targetPosition": "right"}, {"id": "generate:ask_contact", "type": "ragNode", "position": {"x": 0, "y": 0}, "data": {"label": "Generate", "name": "FamousChefsRetire", "form": {"llm_id": "deepseek-chat", "prompt": "You are a customer support. But you can't answer to customers' question. You need to request their contact like E-mail, phone number, Wechat number, LINE number, twitter, discord, etc,. Product experts will contact them later. Please do not ask the same question twice.", "temperature": 0.9, "message_history_window_size": 12, "cite": false}}, "sourcePosition": "left", "targetPosition": "right"}, {"id": "message:get_contact", "type": "ragNode", "position": {"x": 0, "y": 0}, "data": {"label": "Message", "name": "BlueBooksTan", "form": {"messages": ["Okay, I've already write this down. What else I can do for you?", "Get it. What else I can do for you?", "Thanks for your trust! Our expert will contact ASAP. So, anything else I can do for you?", "Thanks! So, anything else I can do for you?"]}}, "sourcePosition": "left", "targetPosition": "right"}]}