.modelContainer {
  width: 100%;
  .factoryOperationWrapper {
    text-align: right;
  }
  .modelItem {
  }
  .llmList {
    padding-top: 10px;
  }
  .toBeAddedCard {
    border-radius: 24px;
    border: 1px solid #eaecf0;
    background: #e3f0ff;
    box-shadow: 0px 1px 2px 0px rgba(16, 24, 40, 0.05);
    :global(.ant-card-body) {
      padding: 10px 24px;
    }
    .addButton {
      padding: 0;
    }
  }
  .toBeAddedCardDark {
    border-radius: 24px;
    border: 1px solid #eaecf0;
    background: #e3f0ff2a;
    box-shadow: 0px 1px 2px 0px rgba(16, 24, 40, 0.05);
    :global(.ant-card-body) {
      padding: 10px 24px;
    }
    .addButton {
      padding: 0;
    }
  }
  .addedCard {
    border-radius: 18px;
    border: 1px solid #eaecf0;
    background: #e6e7eb;
    box-shadow: 0px 1px 2px 0px rgba(16, 24, 40, 0.05);
  }
  .addedCardDark {
    border-radius: 18px;
    border: 1px solid #eaecf0;
    background: #e6e7eb21;
    box-shadow: 0px 1px 2px 0px rgba(16, 24, 40, 0.05);
  }
  .modelDivider {
    margin: 0;
  }
  .modelTags {
    height: 40px;
    overflow: hidden;
    font-size: 8px;
  }
}
