.popoverContent {
  width: 40vw;

  .popoverContentItem {
    display: flex;
    gap: 10px;
  }

  .popoverContentText {
    white-space: pre-line;
    max-height: 50vh;
    overflow: auto;
    .popoverContentErrorLabel {
      color: red;
    }
  }
}

.operationIcon {
  text-align: center;
  display: flex;
  &:hover {
    cursor: pointer;
  }
}
.operationIconSpin {
  animation: spin 1s linear infinite;
  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
}
