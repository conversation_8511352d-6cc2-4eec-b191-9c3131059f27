.testingControlWrapper {
  width: 350px;
  background-color: rgba(255, 255, 255, 0.1);
  padding: 30px 20px;
  overflow: auto;
  height: calc(100vh - 160px);

  .historyTitle {
    padding: 30px 0 20px;
  }
  .historyIcon {
    vertical-align: middle;
  }
  .historyCardWrapper {
    width: 100%;
  }
  .historyCard {
    width: 100%;
    :global(.ant-card-body) {
      padding: 10px;
    }
  }
  .historyText {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1;
  }
}
