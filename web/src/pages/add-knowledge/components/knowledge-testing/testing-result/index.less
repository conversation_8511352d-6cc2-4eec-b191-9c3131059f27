.testingResultWrapper {
  flex: 1;
  background-color: rgba(255, 255, 255, 0.1);
  padding: 30px 20px;
  overflow: auto;
  height: calc(100vh - 160px);
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  .selectFilesCollapse {
    :global(.ant-collapse-header) {
      padding-left: 22px;
    }
    margin-bottom: 32px;
    overflow-y: auto;
  }

  .selectFilesTitle {
    padding-right: 10px;
  }

  .similarityCircle {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
    font-size: 10px;
    font-weight: normal;
  }

  .similarityText {
    font-size: 12px;
    font-weight: 500;
  }
  .image {
    width: 100%;
    max-height: 30vh;
    object-fit: contain;
  }
}
.imagePreview {
  display: block;
  max-width: 45vw;
  max-height: 40vh;
}
